/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-09-26 15:11:15
 * @LastEditTime: 2022-10-12 15:01:03
 * @LastEditors: PhilRandWu
 */
import { Modal, ModalProps } from 'antd';
import React from 'react';
import './styles.less';

interface modelInterface extends ModalProps {
    title: string;
    visible: boolean;
    setVisible?: any;
    okHandle?: () => void;
    onCancelHandle?: any;
    children: any;
    centered?: boolean;
    okText?: string;
    hidden?: any;
    dispaly?: any;
    dispalys?: any;
}
const BaseModal = (props: modelInterface) => {
    return (
        <Modal
            footer={props?.footer}
            okText={props.okText}
            visible={props.visible}
            className={`${props.className} modal`} //组件类名
            closeIcon={props.closeIcon} //图标属性
            maskClosable={false} //取消点击外面关闭
            centered
            onOk={() => {
                props?.okHandle && props?.okHandle();
                // resetSelectFunc()
                // selectTipsObj[status].okHandle(selectVisible)
                // setselectVisible(false)
            }}
            onCancel={() => {
                console.log('testtest', '取消执行');
                props?.onCancelHandle();
                // props?.onCancelHandle && props?.onCancelHandle();
                // resetSelectFunc()
                // setselectVisible(false)
            }}
            width={props?.width ? props?.width : 515}
            title={props.title}
            cancelButtonProps={props?.hidden ? { style: { display: 'none' } } : {}}
            getContainer={props?.getContainer}
            destroyOnClose
        >
            <div className={props?.dispaly ? 'modle-title' : 'none'}>管理员创建成功，请妥善保存账号密码</div>
            <div className={props?.dispalys ? 'modle-titles' : 'none'}>员工账号创建成功，请妥善保存账号密码</div>

            {props.children}
        </Modal>
    );
};

export default BaseModal;
