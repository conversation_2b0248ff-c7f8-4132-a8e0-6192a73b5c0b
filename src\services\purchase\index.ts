/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-11-01 18:29:23
 * @LastEditTime: 2024-09-23 17:30:43
 * @LastEditors: 吴山仁
 */
import request from '../request';

// 收购列表


export const purchasePage = (obj: any) => {
  return request({
      url: '/materialPurchase/getMaterialPurchaseList',
      method: 'post',
      data: obj
  });
};
// 收购新增
export const purchasePageAdd = (obj: any) => {
  return request({
      url: '/materialPurchase/addMaterialPurchase',
      method: 'post',
      data: obj
  });
};
// 收购修改

export const purchasePageEdit = (obj: any) => {
  return request({
      url: '/materialPurchase/modifyMaterialPurchase',
      method: 'post',
      data: obj
  });
};
// 收购详情
export const purchasePageDetail = (obj: any) => {
  return request({
      url: `/materialPurchase/getMaterialPurchaseDetail?id=${obj.productId}`,
      method: 'get',
  });
};
// 收购溯源详情
export const purchaseDetail = (obj: any) => {
  return request({
      url: `/traceData/purchaseDetail?id=${obj.productId}`,
      method: 'get',
  });
};
export const purchaseInfo = (obj: any) => {
  return request({
      url: `/traceData/purchaseInfo?id=${obj.productId}&productionBatch=${obj.productionBatch}`,
      method: 'get',
  });
};
// 获取农作物类型
export const plantNameByLandId = (obj: any) => {
  return request({
      url: '/LandPlantBatch/getPlantNameByLandId',
      method: 'get',
      params: obj
  });
};


// 获取种植批次
export const LandIdAndPlantName = (obj: any) => {
  return request({
      url: '/LandPlantBatch/getPlantBatchByLandIdAndPlantName',
      method: 'get',
      params: obj
  });
};


// 查询被收购次数

export const purchaseCont = (obj: any) => {
  return request({
      url: '/materialPurchase/getPurchaseCount',
      method: 'post',
      data: obj
  });
};

// 启用禁用



export const purchaseUpdata = (obj: any) => {
  return request({
      url: '/materialPurchase/modify-state',
      method: 'post',
      data: obj
  });
};


// 编辑

export const PurchaseBatchExists = (obj: any) => {
  return request({
      url: `/materialPurchase/isPurchaseBatchExists?purchaseBatch=${obj.purchaseBatch}`,
      method: 'get',
      // data: obj
  });
};

// 获取链上哈希
export const getChainHisListDetail = (obj: any) => {
  return request({
      url: `/materialPurchase/getChainHisList?id=${obj.productId}`,
      method: 'get',
  });
};

// 查询农户是否注册
export const checkRegister = (data: any) => {
    return request({
        url: `/materialPurchase/checkFarmer`,
        method: 'post',
        data: data
    });
};

// 农户注册账号
export const registerAccount = (data: any) => {
    return request({
        url: `/user/addFarmer`,
        method: 'post',
        data: data
    });
};





















//食品列表数据
export const foodPage = (obj: any) => {
    return request({
        url: '/product/getProductList',
        method: 'post',
        data: obj
    });
};
//修改食品状态
export const FoodState = (obj: any) => {
    // console.log("obj777",obj)
    return request({
        url: `/product/updateState`,
        method: 'post',
        data: obj
    });
};
//查看食品详情
export const foodDetail = (obj: any) => {
    return request({
        url: `/product/getProductDetail?productId=${obj.productId}`,
        method: 'get',
    });
};
//新增食品
export const addFood = (obj: any) => {
    return request({
        url: '/product/addProduct',
        method: 'post',
        data: obj
    });
};
//配置食品溯源码
export const sourceConfig = (obj: any) => {
    return request({
        url: '/food/sourceConfig',
        method: 'post',
        data: obj
    });
};
//编辑食品
export const updateFood = (obj: any) => {
    return request({
        url: '/product/modifyProduct',
        method: 'post',
        data: obj
    });
};
//食品列表
export const materialList = (obj: any) => {
    return request({
        url: `/food/materialList?foodId=${obj.foodId}`,
        method: 'post',
        data: obj
    });
};
//食品品类
export const foodCategory = (obj: any) => {
    return request({
        url: `/category/getCategoryList`,
        method: 'get'
    })
};
//上传图片
export const temporaryUploadUrl = (obj: any) => {
    return request({
        url: `/minio/get-url`,
        method: 'get',
        params: obj
    });
};
export const getFoodInfo = (obj: any) => {
    return request({
        url: `/food/getFoodInfo?foodId=${obj.id}`,
        method: 'post',
        data: obj
    });
};
// 产品选择列表
export const getProductSelectList = (obj: any) => {
    return request({
        url: `/product/list`,
        method: 'get',
        params: obj
    });
};
// 产地配置可选产品列表
export const getProductSelectListInPlaceSelect = (obj: any) => {
    return request({
        url: `/product/listProduct`,
        method: 'get',
        params: obj
    });
};
// 查询溯源码可配置项
export const getProductConfigOptions = () => {
    return request({
        url: `/product/configList`,
        method: 'get',
        params: {}
    });
};
// 查询溯源码当前配置项
export const getProductConfigs = (obj: any) => {
    return request({
        url: `/product/getConfig`,
        method: 'get',
        params: obj
    });
};
// 配置溯源码
export const configSourceCode = (obj: any) => {
    return request({
        url: `/product/config`,
        method: 'post',
        data: obj
    });
};