import React, { useEffect } from 'react';
import { Form, message, Button, Input, Select } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import delIcon from '@/assets/icon/del.svg';
import BaseModal from '@components/base-modal';
import { useMutation, useQuery } from 'react-query';
import {
    getSalesChannelList,
    getProductSalesChannels,
    saveProductSalesChannels,
    validateChannelCode
} from '@services/sales-channel';
import { ReformChainError } from '@utils/errorCodeReform';

const { Option } = Select;

// 销售渠道选项数据
const salesChannelOptions = [
    { id: '1', name: '本来生活', isRequired: true },
    { id: '2', name: '卓望公司', isRequired: false }
];

interface SalesChannelModalProps {
    visible: boolean;
    onCancel: () => void;
    product: any;
    onSuccess: () => void;
}

interface SalesChannelItem {
    channelId: string;
    productCode?: string;
}

const SalesChannelModal: React.FC<SalesChannelModalProps> = ({ visible, onCancel, product, onSuccess }) => {
    const [form] = Form.useForm();

    // 获取产品已关联的销售渠道
    const productSalesChannelsQuery = useQuery(
        ['productSalesChannels', product?.id],
        () => getProductSalesChannels({ productId: product?.id }),
        {
            enabled: !!product?.id && visible,
            onSuccess(data) {
                const channels = data?.data || [];
                // 设置表单初始值
                form.setFieldsValue({
                    channels: channels.length > 0 ? channels : []
                });
            },
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );

    // 保存销售渠道配置
    const saveMutation = useMutation(saveProductSalesChannels, {
        onSuccess() {
            message.success('销售渠道维护成功');
            onSuccess();
        },
        onError(err: any) {
            ReformChainError(err);
        }
    });

    // 验证渠道产品编码
    const validateCodeMutation = useMutation(validateChannelCode, {
        onError(err: any) {
            ReformChainError(err);
        }
    });

    // 获取渠道配置信息
    const getChannelConfig = (channelId: string) => {
        const channel = salesChannelOptions.find((item) => item.id === channelId);
        return {
            name: channel?.name || '',
            isRequired: channel?.isRequired || false,
            placeholder: '请输入产品编码'
        };
    };

    // 表单提交处理
    const onFinish = async (values: any) => {
        try {
            const channels = values.channels || [];

            // 验证逻辑
            const errors: string[] = [];
            const usedCodes: { [key: string]: string[] } = {};

            for (let i = 0; i < channels.length; i++) {
                const channel = channels[i];

                if (!channel.channelId) {
                    errors.push(`第${i + 1}行：请选择销售渠道`);
                    continue;
                }

                const channelConfig = getChannelConfig(channel.channelId);

                if (channelConfig.isRequired && !channel.productCode?.trim()) {
                    errors.push(`第${i + 1}行：${channelConfig.name}渠道必须填写产品编码`);
                    continue;
                }

                if (channel.productCode?.trim()) {
                    // 检查同一产品内的重复编码
                    const channelKey = channel.channelId;
                    if (!usedCodes[channelKey]) {
                        usedCodes[channelKey] = [];
                    }

                    if (usedCodes[channelKey].includes(channel.productCode)) {
                        errors.push(`当前产品在${channelConfig.name}渠道已关联产品编码，请先删除原编码再添加`);
                        continue;
                    }
                    usedCodes[channelKey].push(channel.productCode);

                    // 检查跨产品的重复编码
                    try {
                        const validateResult = await validateCodeMutation.mutateAsync({
                            channelId: channel.channelId,
                            productCode: channel.productCode,
                            productId: product?.id
                        });

                        if (!validateResult.data.valid) {
                            errors.push(`当前产品在${channelConfig.name}渠道已关联产品编码，请先删除原编码再添加`);
                        }
                    } catch (error) {
                        console.error('验证产品编码失败:', error);
                    }
                }
            }

            if (errors.length > 0) {
                message.error(errors[0]);
                return;
            }

            // 保存数据
            await saveMutation.mutateAsync({
                productId: product?.id,
                channels: channels.filter((channel: any) => channel.channelId)
            });
        } catch (error) {
            console.error('保存失败:', error);
        }
    };

    // 重置表单
    useEffect(() => {
        if (visible && product) {
            // 当弹窗打开时，如果没有数据则设置空的初始值
            if (!productSalesChannelsQuery.data?.data?.length) {
                form.setFieldsValue({
                    channels: []
                });
            }
        }
    }, [visible, product, form, productSalesChannelsQuery.data]);

    return (
        <BaseModal
            title='维护销售渠道信息'
            visible={visible}
            onCancelHandle={onCancel}
            okHandle={() => form.submit()}
            okText='确定'
            width={600}
        >
            <Form form={form} name='sales_channel_form' onFinish={onFinish} autoComplete='off'>
                {/* 表头 */}
                <div
                    style={{
                        display: 'flex',
                        padding: '8px 0',
                        fontWeight: 600,
                        fontSize: '18px',
                        color: '#262626'
                    }}
                >
                    <div style={{ width: 200, marginRight: 16 }}>
                        <span style={{ color: '#ff4d4f', marginRight: '4px' }}>*</span>
                        销售渠道：
                    </div>
                    <div style={{ width: 250, marginRight: 16 }}>
                        <span style={{ color: '#ff4d4f', marginRight: '4px' }}>*</span>
                        渠道产品编码：
                    </div>
                </div>

                <Form.List name='channels'>
                    {(fields, { add, remove }) => (
                        <>
                            {fields.map((field) => (
                                <div
                                    key={field.key}
                                    style={{
                                        display: 'flex',
                                        marginBottom: 10,
                                        alignItems: 'flex-start',
                                        padding: '8px 0'
                                    }}
                                >
                                    <Form.Item
                                        {...field}
                                        name={[field.name, 'channelId']}
                                        rules={[{ required: true, message: '请选择销售渠道' }]}
                                        style={{ marginBottom: 0, width: 200, marginRight: 16 }}
                                    >
                                        <Select
                                            placeholder='请选择销售渠道'
                                            onChange={(value) => {
                                                // 当渠道切换时，清除产品编码字段的验证错误
                                                form.setFields([
                                                    {
                                                        name: ['channels', field.name, 'productCode'],
                                                        errors: []
                                                    }
                                                ]);

                                                // 如果切换到不需要产品编码的渠道，清空产品编码值
                                                const channelConfig = getChannelConfig(value);
                                                if (!channelConfig.isRequired) {
                                                    form.setFieldValue(['channels', field.name, 'productCode'], '');
                                                }
                                            }}
                                        >
                                            {salesChannelOptions.map((item) => (
                                                <Option key={item.id} value={item.id}>
                                                    {item.name}
                                                </Option>
                                            ))}
                                        </Select>
                                    </Form.Item>

                                    <Form.Item
                                        noStyle
                                        shouldUpdate={(prevValues, curValues) =>
                                            prevValues.channels !== curValues.channels
                                        }
                                    >
                                        {() => {
                                            const currentChannelId = form.getFieldValue([
                                                'channels',
                                                field.name,
                                                'channelId'
                                            ]);
                                            const channelConfig = getChannelConfig(currentChannelId);

                                            return (
                                                <div style={{ width: 250, marginRight: 16 }}>
                                                    <Form.Item
                                                        {...field}
                                                        name={[field.name, 'productCode']}
                                                        rules={
                                                            channelConfig.isRequired
                                                                ? [
                                                                      {
                                                                          required: true,
                                                                          message: `${channelConfig.name}渠道必须填写产品编码`
                                                                      }
                                                                  ]
                                                                : []
                                                        }
                                                        style={{ marginBottom: 0 }}
                                                    >
                                                        <Input placeholder={channelConfig.placeholder} />
                                                    </Form.Item>
                                                    {!channelConfig.isRequired && currentChannelId && (
                                                        <div
                                                            style={{
                                                                fontSize: '12px',
                                                                color: '#999',
                                                                marginTop: '4px',
                                                                lineHeight: '1.2'
                                                            }}
                                                        >
                                                            <span style={{ color: '#ff4d4f', marginRight: '4px' }}>
                                                                *
                                                            </span>
                                                            当前销售渠道不需要填写产品编码
                                                        </div>
                                                    )}
                                                </div>
                                            );
                                        }}
                                    </Form.Item>

                                    <div
                                        style={{
                                            display: 'flex',
                                            justifyContent: 'center',
                                            alignItems: 'center'
                                        }}
                                    >
                                        <img
                                            src={delIcon}
                                            alt='删除'
                                            onClick={() => remove(field.name)}
                                            style={{
                                                width: '28px',
                                                height: '28px',
                                                cursor: 'pointer',
                                                borderRadius: '4px',
                                                transition: 'all 0.3s ease',
                                                padding: '6px 4px 4px 1px'
                                            }}
                                            // onMouseEnter={(e) => {
                                            //     e.currentTarget.style.backgroundColor = '#fff2f0';
                                            //     e.currentTarget.style.transform = 'scale(1.1)';
                                            // }}
                                            // onMouseLeave={(e) => {
                                            //     e.currentTarget.style.backgroundColor = 'transparent';
                                            //     e.currentTarget.style.transform = 'scale(1)';
                                            // }}
                                        />
                                    </div>
                                </div>
                            ))}

                            <Form.Item style={{ width: 468 }}>
                                <Button
                                    type='dashed'
                                    className='primaryBtn'
                                    onClick={() => add()}
                                    block
                                    icon={<PlusOutlined />}
                                    style={{
                                        height: '40px',
                                        borderStyle: 'solid',
                                        borderColor: '#80a932',
                                        background: '#ffffff'
                                    }}
                                >
                                    添加渠道
                                </Button>
                            </Form.Item>
                        </>
                    )}
                </Form.List>
            </Form>
        </BaseModal>
    );
};

export default SalesChannelModal;
