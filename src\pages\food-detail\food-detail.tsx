/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-10 17:15:57
 * @LastEditTime: 2024-10-10 16:09:57
 * @LastEditors: 吴山仁
 */
import BaseCard from '@components/base-card';
import BaseFormItem from '@components/base-form-item';
import PageTitle from '@components/page-title';
import React, { useState } from 'react';
import { Form, message } from 'antd';
import { useMutation, useQuery } from 'react-query';
import { foodDetail } from '@services/food';
import { enumeration } from './config';
import SwitchList from '@components/switch-list';
import styles from './index.module.less';
import { useLocation } from 'react-router-dom';
import { ReformChainError } from '@utils/errorCodeReform';
import dayjs from 'dayjs';
import { Image } from 'antd';
import FilterForm from '@components/filter-form';

import { FormItemImages, FormItemVideo } from '@components';
import ChainDetailModal from '@components/chain_detail_modal';
import { decryptedUrl, isArrayArr } from '@utils';

const FoodDetail = (props: any) => {
    const { state } = useLocation();
    const [detailForm] = Form.useForm();

    const [ChainDetailModalVisible, setChainDetailModalVisible] = useState(false);
    // console.log("state",state)

    const detailquery = useQuery(
        ['detailquery9999'],
        () => {
            return foodDetail({
                productId: state?.id
            });
        },
        {
            async onSuccess(res) {
                console.log('res0990099', res);
                const arrayData = await Promise.all(
                    isArrayArr(res?.data?.productImg)?.map((item: any) => {
                        return decryptedUrl(item);
                    })
                );
                // setPromotionPicData(arrayData)
                const videos = await decryptedUrl(res?.data?.productVideo ? res?.data?.productVideo : null);
                // 克（g） 千克（kg）
                const Unit = res?.data?.specificationUnit === 1 ? '克（g）' : '千克（kg）';
                detailForm.setFieldsValue({
                    ...res.data,
                    productImg: arrayData && arrayData.length > 0 ? arrayData : null,
                    productVideo: videos ? videos : null,
                    specification: res?.data?.specification ? res?.data?.specification + Unit : '-',
                    transactionTime: res?.data?.transactionTime
                        ? dayjs(res?.data?.transactionTime).format('YYYY-MM-DD HH:mm:ss')
                        : '-'
                });
            },
            onError(err: any) {
                ReformChainError(err);
            }
        }
    );
    // console.log("detailquery",detailquery)
    const fooddetail = detailquery?.data?.data?.food;

    const foodInfoConfig = [
        {
            label: '产品名称',
            name: 'productName',
            value: 'productName',
            type: 'ShowText'
        },
        {
            label: '产品品类',
            name: 'productCategory',
            value: 'productCategory',
            type: 'ShowText'
        },
        {
            label: '保质期',
            name: 'expirationDate',
            value: 'expirationDate',
            type: 'ShowText'
        },
        // {
        //     label: '产品编码',
        //     name: 'code',
        //     value: 'productCode',
        //     type: 'ShowText'
        // },
        {
            label: '产品执行标准',
            name: 'executiveStandard',
            value: 'executiveStandard',
            type: 'ShowText'
        },
        {
            label: '食品生产许可证编号',
            name: 'productionLicense',
            value: 'productionLicense',
            type: 'ShowText'
        },
        {
            label: '配料',
            name: 'ingredient',
            value: 'ingredient',
            type: 'ShowText'
        },
        {
            label: '产品规格',
            name: 'specification',
            value: 'specification',
            type: 'ShowText'
        }
        // {
        //     label: '产品合格证明',
        //     name: 'productAptitude',
        //     value: 'productAptitude',
        //     type: 'Custom',
        //     children: (
        //         <Form.Item key='promotionPic' name='productAptitude'>
        //             <FormItemImages width={150} />
        //         </Form.Item>
        //     )
        // }
    ];

    const productionConfig = [
        {
            label: '产品介绍',
            name: 'productIntro',
            value: 'productIntro',
            type: 'ShowScrollText'
        },
        {
            label: '宣传图片',
            name: 'productImg',
            value: 'productImg',
            type: 'Custom',
            children: (
                <Form.Item key='productImg' name='productImg'>
                    <FormItemImages height={100}></FormItemImages>
                </Form.Item>
            )
        },
        {
            label: '宣传视频',
            name: 'productVideo',
            value: 'productVideo',
            type: 'Custom',
            children: (
                <Form.Item key='productVideo' name='productVideo'>
                    <FormItemVideo width={200} controls></FormItemVideo>
                </Form.Item>
            )
        }
    ];

    const ChainDetailModalConfig = {
        transactionId: detailForm.getFieldValue('transactionId'),
        open: ChainDetailModalVisible,
        onCancel: () => setChainDetailModalVisible(false)
    };

    const chainConfig = [
        {
            label: '链上哈希',
            name: 'transactionId',
            value: 'transactionId',
            title: '信息的链上的哈希值',
            type: 'Link',
            onClick: () => {
                setChainDetailModalVisible(true);
            }
        },
        {
            label: '上链时间',
            name: 'transactionTime',
            value: 'transactionTime',
            title: '信息上链的时间',
            type: 'ShowText'
        }
    ];

    const onFinish = (values: any) => {
        console.log('Success:', values);
    };

    const onFinishFailed = (errorInfo: any) => {
        console.log('Failed:', errorInfo);
    };
    console.log('detailquery?.data?.data?.material', detailquery?.data?.data?.material);
    return (
        <div>
            <BaseCard title={<PageTitle title='产品详情' bg='container chan' />}>
                <Form
                    name='basic'
                    form={detailForm}
                    onFinish={onFinish}
                    onFinishFailed={onFinishFailed}
                    autoComplete='off'
                >
                    {/* <PageTitle title='食品信息' type='primaryIcon' />
                    <BaseFormItem configs={foodInfoConfig} /> */}
                    <PageTitle title='产品基础信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={foodInfoConfig} labelCol={false} />

                    <PageTitle title='产品简介' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={productionConfig} labelCol={false} />

                    <PageTitle title='区块链信息' type='primaryIcon' bmagin={16} />
                    <FilterForm showMode itemConfig={chainConfig} labelCol={false} />
                </Form>
            </BaseCard>
            <ChainDetailModal {...ChainDetailModalConfig} />
        </div>
    );
};

export default FoodDetail;
