/*
 * @Description:
 * @Author: PhilRandWu
 * @Github: https://github/PhilRandWu
 * @Date: 2022-10-10 10:01:45
 * @LastEditTime: 2024-10-10 10:28:48
 * @LastEditors: 吴山仁
 */
import BaseButton from '@components/base-button';
import BaseCard from '@components/base-card';
import FilterForm from '@components/filter-form';
import PageTitle from '@components/page-title';

import { Form, message, Upload, Button, Select, InputNumber, Modal } from 'antd';
import { useMutation, useQuery } from 'react-query';
import React, { Component, useEffect, useState, useMemo } from 'react';
import styles from './index.module.less';
import { ReformChainError } from '@utils/errorCodeReform';
import { useNavigate } from 'react-router-dom';
import { signData } from '../../utils/blockChainUtils';
import { useDispatch } from 'react-redux';
import { LandSourceService } from '@services/land-test';
import copyToClipboard from 'copy-to-clipboard';
import {
    purchasePageAdd,
    plantNameByLandId,
    LandIdAndPlantName,
    purchaseCont,
    checkRegister,
    registerAccount
} from '@services/purchase';
import { randomPassword } from '@utils';
import BaseSelect from '@components/base-select';
import rsaEncrypt from '@utils/rsa';
import BaseModal from '@components/base-modal';
const FoodAdd = (props: any) => {
    const [options, setOptions]: any = useState([]);
    const [search] = Form.useForm();
    const [uploadImageUrl, setUploadImageUrl]: any = useState('');
    const [url, setUrl]: any = useState('');
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const [isUpLoading, setIsUpLoading]: any = useState();
    const [fooddata, setfooddata]: any = useState('');
    const [n, setn] = useState(1);
    const [checkRegisterModal, setCheckRegisterModal] = useState(false);
    const [formValues, setFormValues] = useState({
        farmerName: '',
        phoneNumber: ''
    }); // 用于存储表单值
    const [canSubmit, setCanSubmit] = useState(false);

    //产品品类
    // const foodcategory = useMutation(foodCategory, {
    //     onSuccess(res) {
    //         // getLocalPrivatekey(dispatch);
    //         // @ts-ignore
    //         const mapOptions = (items: any[]) => {
    //             return items.map((item) => {
    //                 if (item.childrenCategory) {
    //                     return {
    //                         label: item.categoryName,
    //                         // value: item.id,
    //                         value: item.categoryName,
    //                         children: mapOptions(item.childrenCategory)
    //                     };
    //                 } else {
    //                     return {
    //                         label: item.categoryName,
    //                         value: item.id
    //                     };
    //                 }
    //             });
    //         };
    //         setOptions(mapOptions(res?.data || []));
    //     },
    //     onError(err: any) {
    //         ReformChainError(err);
    //     }
    // });

    // 新增收购
    const purchasepageAdd = useMutation(purchasePageAdd, {
        onSuccess(res) {
            message.success('新增收购成功');
            navigate('/purchase/list');
        },
        onError(err: any) {
            ReformChainError(err);
        }
    });

    useMemo(() => {
        search.setFieldsValue({
            // ...formInfo,
            userName: JSON.parse(localStorage.getItem('userdata') || '').name
        });
    }, []);

    // const purchasePageAdd = useMutation(purchasePageAdd, {
    //     onSuccess(res) {
    //         message.success('新增产品成功');
    //         navigate('/product-manage/food');
    //     },
    //     onError(err: any) {
    //         ReformChainError(err);
    //     }
    // });

    const [purchaseTimesId, setPurchaseTimesId] = useState(null);

    // const purchaseTimeData = useQuery(
    //     ['purchaseCont', purchaseTimesId],
    //     async () => {
    //         return purchaseCont({
    //             farmerName: search.getFieldsValue().farmerName,
    //             phoneNumber: await rsaEncrypt(String(search.getFieldsValue().phoneNumber))
    //         });
    //     },
    //     {
    //         enabled: Boolean(purchaseTimesId) // 只有当selectedLandId有值时才启用查询
    //     }
    // );
    const purchaseTimeData = useQuery(
        ['purchaseCont', search.getFieldsValue().farmerName, search.getFieldsValue().phoneNumber],
        async () => {
            return purchaseCont({
                farmerName: search.getFieldsValue().farmerName,
                phoneNumber: await rsaEncrypt(String(search.getFieldsValue().phoneNumber))
            });
        },
        {
            async onSuccess(res) {
                search.setFieldsValue({
                    purchaseTimes: res.data || 0
                });
            },
            retry: false
        }
    );
    const addBasicInfoConfigs = [
        {
            type: 'Input',
            label: '农户姓名',
            value: 'farmerName',
            maxLength: 30,
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(/^[\u4e00-\u9fa5_a-zA-Z0-9_]{1,30}$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback('请输入农户姓名!');
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (verify === false) {
                            if (value.length > 30) {
                                callback('请保持字符在30字符以内!');
                            } else {
                                callback('请输入农户姓名，支持中文、字母或数字!');
                            }
                        } else {
                            callback();
                        }
                    }
                })
            ],
            placeholder: '请输入农户姓名',
            onBlur: (e: any) => {
                setPurchaseTimesId(e.target.value);
                search.setFieldsValue({
                    farmerName: e.target.value
                });
                console.log(purchaseTimeData.data?.data, '1111111111111111111');
                search.setFieldsValue({
                    purchaseTimes: purchaseTimeData.data?.data || 0
                });
                // 这里是失去焦点时触发的事件处理器
                // const value = e.target.value;
                // if (!value) {
                //     // 可以在这里触发Ant Design Form的验证
                //     form.validateFields(['phoneNumber']).catch(() => {});
                // }
            }
        },
        {
            label: '联系方式',
            type: 'Input',
            value: 'phoneNumber',
            placeholder: '请输入联系方式',
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(/^(?:(?:\+|00)86)?1[3-9]\d{9}$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback('请输入联系方式！');
                        } else if (verify === false) {
                            callback('请输入正确的手机号！');
                        } else {
                            callback();
                        }
                    }
                })
            ],
            onBlur: (e: any) => {
                setPurchaseTimesId(e.target.value);
                search.setFieldsValue({
                    phoneNumber: e.target.value
                });
                console.log(purchaseTimeData.data?.data, '1111111111111111111');
                search.setFieldsValue({
                    purchaseTimes: purchaseTimeData.data?.data || 0
                });
            }
        },
        {
            type: 'Input',
            label: '被收购次数',
            value: 'purchaseTimes',
            // maxLength: 30,
            // rules: [
            //     { required: true, message: '' },
            //     () => ({
            //         validator: (_: any, value: any, callback: any) => {
            //             const regExp = new RegExp(/^[\u4e00-\u9fa5_a-zA-Z0-9_]{1,30}$/);
            //             const verify = regExp.test(value);
            //             if (!value) {
            //                 callback('请输入被收购次数!');
            //             } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
            //                 callback('字段前后不能输入空格！');
            //             } else if (verify === false) {
            //                 if (value.length > 30) {
            //                     callback('请保持字符在30字符以内!');
            //                 } else {
            //                     callback('请输入被收购次数，支持中文、字母或数字!');
            //                 }
            //             } else {
            //                 callback();
            //             }
            //         }
            //     })
            // ],
            disable: true,
            rules: [{ required: true, message: '' }],
            placeholder: ''
        }
    ];

    const [selectedLandId, setSelectedLandId] = useState(null);
    // 种植批次;
    const [selectedLandBatch, setSelectedLandBatch] = useState(null);
    // 获取地块名
    const landProductSele = useQuery(['landProductSele'], () => LandSourceService());

    const landProductListData: any[] = landProductSele?.data?.data;
    // 获取农作物类型
    const plantNameByLandIdSele = useQuery(
        ['plantNameByLandId', selectedLandId],
        () => {
            return plantNameByLandId({
                landId: selectedLandId
            });
        },
        {
            enabled: Boolean(selectedLandId) // 只有当selectedLandId有值时才启用查询
        }
    );

    const plantNameByLandIdData = plantNameByLandIdSele?.data?.data;
    // 获取种植批次
    const plantNameByLandIdSeleName = useQuery(
        ['LandIdAndPlantName', selectedLandBatch],
        () => {
            return LandIdAndPlantName({
                landId: selectedLandId,
                plantName: selectedLandBatch
            });
        },
        {
            enabled: Boolean(selectedLandBatch) // 只有当selectedLandId有值时才启用查询
        }
    );

    const plantNameByLandIdNameData = plantNameByLandIdSeleName?.data?.data;

    const addIntroductionConfigs = [
        {
            label: '地块名称',
            value: 'landName',
            type: 'Custom',
            rules: [{ required: true, message: '请选择地块名称!' }],
            children: (
                <BaseSelect
                    // defaultValue={1}
                    placeholder='请选择地块名称'
                    options={landProductListData?.map((item) => ({
                        label: item?.landName,
                        value: item?.landId
                    }))}
                    onChange={(value) => {
                        if (value) {
                            setSelectedLandId(value);
                            search.setFieldsValue({ landType: undefined });
                            search.setFieldsValue({ plantBatch: undefined });
                            setSelectedLandBatch(null);
                        } else {
                            // setData2([]);
                            // search.setFieldsValue('landType');
                        }
                    }}
                ></BaseSelect>
            )
        },

        {
            label: '农作物类型',
            value: 'landType',
            type: 'Custom',
            rules: [{ required: true, message: '请选择农作物类型!' }],
            children: (
                <BaseSelect
                    // defaultValue={1}
                    placeholder='请选择农作物类型'
                    options={plantNameByLandIdData?.map((item: any) => ({
                        label: item?.plantName,
                        value: item?.plantName
                    }))}
                    onChange={(value) => {
                        if (value) {
                            setSelectedLandBatch(value);
                            search.setFieldsValue({ plantBatch: undefined });
                        } else {
                            // setData2([]);
                            // search.setFieldsValue('landType');
                        }
                    }}
                    disabled={!selectedLandId}
                ></BaseSelect>
            )
        },
        {
            label: '种植批次',
            value: 'plantBatch',
            type: 'Custom',
            rules: [{ required: true, message: '请选择种植批次!' }],
            children: (
                <BaseSelect
                    // defaultValue={1}
                    placeholder='请选择种植批次'
                    options={plantNameByLandIdNameData?.map((item: any) => ({
                        label: item?.plantBatch,
                        value: item?.plantBatch,
                        disabled: item?.state == 1 // 添加禁用逻辑
                    }))}
                    onChange={(value) => {
                        console.log(value);
                    }}
                    disabled={!selectedLandBatch}
                ></BaseSelect>
            )
        },
        // {
        //     type: 'Select',
        //     label: '种植批次',
        //     value: 'plantBatch',
        //     rules: [{ required: true, message: '请选择种植批次!' }],
        //     placeholder: '请选择',
        //     // className: 'rawMaterial',
        //     fields: data3
        // },
        {
            type: 'Input',
            label: '收购对接人',
            value: 'userName',
            placeholder: '请输入收购对接人',
            maxLength: 30,
            disable: true,
            rules: [{ required: true, message: '请输入收购对接人!' }]
        },
        {
            type: 'Input',
            label: '收购批次',
            maxLength: 50,
            value: 'purchaseBatch',
            placeholder: '请输入收购批次',
            rules: [
                { required: true, message: '' },
                () => ({
                    validator: (_: any, value: any, callback: any) => {
                        const regExp = new RegExp(/^[\u4e00-\u9fa5_a-zA-Z0-9_]{1,50}$/);
                        const verify = regExp.test(value);
                        if (!value) {
                            callback('请输入收购批次!');
                        } else if (value[0] == ' ' || value[value.length - 1] == ' ') {
                            callback('字段前后不能输入空格！');
                        } else if (verify === false) {
                            if (value.length > 50) {
                                callback('请保持字符在50字符以内!');
                            } else {
                                callback('请输入收购批次，支持中文、字母或数字!');
                            }
                        } else {
                            callback();
                        }
                    }
                })
            ]
        },
        {
            type: 'DatePicker',
            label: '收购时间',
            value: 'purchaseTime',
            placeholder: '请选择收购时间',
            wide: '100%',
            showTime: true,
            rules: [{ required: true, message: '请选择收购时间!' }]
        },
        {
            type: 'Custom',
            label: '收购重量',
            value: 'purchaseWeight',

            rules: [{ required: true, message: '请输入收购重量!' }],
            children: (
                <InputNumber
                    addonAfter='吨'
                    placeholder='请输入收购重量'
                    min={0}
                    max={100000}
                    step={0.01} // 设置步长为0.01确保每次改变都是0.01的倍数
                    precision={2} // 限制输入数值保留两位小数
                    style={{ width: '100%', height: '34px' }}
                    onChange={(value) => {
                        // 可以在此处添加额外的验证逻辑，例如处理非数字输入或超过预期范围的值
                    }}
                />
            )
        },
        {
            type: 'Custom',
            label: '装袋数量',
            value: 'bagCount',
            rules: [  { required: true, message: '请输入装袋数量!' },
                            {
                                validator: (_: any, value: any) => {
                                    if (value === 0 || value === '0') {
                                        return Promise.reject(new Error('装袋数量必须大于0'));
                                    }
                                    return Promise.resolve();
                                }
                            }],
            children: (
                <InputNumber
                    addonAfter='袋'
                    placeholder='请输入装袋数量'
                    min={0}
                    max={10000000}
                    step={0.1} // 设置步长为0.01确保每次改变都是0.01的倍数
                    precision={1} // 限制输入数值保留两位小数
                    style={{ width: '100%', height: '34px' }}
                    onChange={(value) => {
                        // 可以在此处添加额外的验证逻辑，例如处理非数字输入或超过预期范围的值
                    }}
                />
            )
        },
        {
            type: 'Custom',
            label: '收购单价',
            value: 'purchaseUnitPrice',

            rules: [{ required: true, message: '请输入收购单价!' }],
            children: (
                <InputNumber
                    addonAfter='元/吨'
                    placeholder='请输入收购单价'
                    min={0}
                    max={100000}
                    step={0.01} // 设置步长为0.01确保每次改变都是0.01的倍数
                    precision={2} // 限制输入数值保留两位小数
                    style={{ width: '100%', height: '34px' }}
                    onChange={(value) => {
                        // 可以在此处添加额外的验证逻辑，例如处理非数字输入或超过预期范围的值
                    }}
                />
            )
        }
    ];
    useEffect(() => {
        const generateUniqueString = () => {
            const date = new Date();
            const dateString = date.toISOString().slice(0, 10).replace(/-/g, ''); // 获取当前日期并转换成YYYYMMDD格式
            const randomNumber = Math.floor(1000 + Math.random() * 9000); // 生成一个介于1000到9999之间的随机数
            return `${dateString}${randomNumber}`;
        };
        search.setFieldsValue({
            purchaseBatch: generateUniqueString()
            // grower: selectedLand[0].farmerName
        });
    }, []);
    const onFinish = (values: any) => {
        // setsuccessModalVisible(true);
        // 判断注册农户还没有有后台接口（09.27）
        setFormValues((preData) => ({ ...preData, ...values })); // 设置表单值以触发查询
        setCanSubmit(true);
        // 不判断,原有逻辑
        // handleEncryptedFormData(values);
    };

    // 加密表单数据
    const handleEncryptedFormData = (values: any) => {
        const params: any = {
            farmerName: values.farmerName,
            // landName: values.landName,
            // landType: values.landType,
            // productCategory: values.productCategory.join('>'),
            phoneNumber: values.phoneNumber,
            plantBatch: values.plantBatch,
            userName: values.userName,
            purchaseBatch: values.purchaseBatch,
            purchaseWeight: values.purchaseWeight,
            purchaseUnitPrice: values.purchaseUnitPrice,
            purchaseTime: values.purchaseTime,
            bagCount: values.bagCount
            // productImg: getFileUrlFormUploadedFile(values?.productImg),
            // productVideo: url
        };

        const paramStr = JSON.stringify(params);
        signData(dispatch, JSON.stringify(params), (error, result: any) => {
            if (!error && result) {
                purchasepageAdd.mutate({
                    addMaterialPurchaseVo: params,
                    paramStr: paramStr,
                    signature: result
                });
            } else if (error !== 'misprivatekey') {
                message.info('签名异常，请重试或联系管理员');
            }
        });
    };
    // 判断农户是否已注册
    const handleCheckRegister = useQuery(
        'checkRegister',
        async () => {
            return checkRegister({
                farmerName: formValues.farmerName,
                phoneNumber: await rsaEncrypt(String(formValues.phoneNumber))
            });
        },
        {
            enabled: false,
            onSuccess: (res) => {
                // res.data === true 已注册
                // res.data === false 未注册
                setCanSubmit(false);
                if (res.data === true) {
                    handleEncryptedFormData(formValues);
                }
                if (res.data === false) {
                    setCheckRegisterModal(true);
                }
            },
            onError: (err) => {
                setCanSubmit(false);
                ReformChainError(err);
            },
            retry: false
        }
    );

    // 保证formValues更新。
    useEffect(() => {
        if (formValues.farmerName && formValues.phoneNumber && canSubmit) {
            handleCheckRegister.refetch();
        }
    }, [canSubmit]);

    // 农户注册账号
    const handleRegister = async () => {
        const randomPd = randomPassword(8);
        setCreatePassword(randomPd);
        saveRegister.mutate({
            farmerName: formValues.farmerName,
            phoneNumber: await rsaEncrypt(String(formValues.phoneNumber)),
            password: await rsaEncrypt(randomPd)
        });
    };

    const saveRegister = useMutation(registerAccount, {
        onSuccess(res) {
            setsuccessModalVisible(true);
            setCheckRegisterModal(false);
        },
        onError(err: any) {
            ReformChainError(err);
        }
    });
    // 复制密码
    const [successModalVisible, setsuccessModalVisible] = useState(false);
    const [createPassword, setCreatePassword] = useState('');
    const [addAccountForm] = Form.useForm();
    //创建数据
    const successConfigs = [
        {
            label: '用户名',
            value: 'farmerName',
            placeholder: '请输入管理员名称',
            className: 'manaName',
            display: search.getFieldValue('farmerName')
        },
        {
            label: '联系方式',
            value: 'phoneNumber',
            placeholder: '请输入',
            className: 'phone',
            display: search.getFieldValue('phoneNumber')
        },
        {
            label: '密码',
            value: 'password',
            placeholder: '请输入',
            className: 'phone',
            display: '. . . . . . . . . .'
        }
    ];
    const successConfig = {
        okText: '复制密码',
        title: '用户创建成功，请妥善保存账号和密码！',
        // hidden: true,
        visible: successModalVisible,
        setVisible: setsuccessModalVisible,
        okHandle: async () => {
            setsuccessModalVisible(false);
            const username: any = {
                // account: userAdd?.data?.data?.account,
                password: createPassword
            };
            const copyRet = copyToClipboard(`${username.password}`);
            copyRet ? message.success('复制成功') : message.error('复制失败');
            addAccountForm.resetFields();
            handleEncryptedFormData(formValues);

            // navigate('/purchase/list');
        },
        onCancelHandle: () => {
            setsuccessModalVisible(false);
            addAccountForm.resetFields();
            handleEncryptedFormData(formValues);

            // navigate('/purchase/list');
        }
    };
    return (
        <BaseCard title={<PageTitle title='新建收购信息' bg='container chan' />}>
            <PageTitle title='农户信息' type='primaryIcon' bmagin={16} />
            <Form onFinish={onFinish} className='edit-label-title' form={search}>
                <FilterForm itemConfig={addBasicInfoConfigs} labelCol={3} wrapperCol={9} />

                <PageTitle title='收购信息' type='primaryIcon' bmagin={16} />

                <FilterForm itemConfig={addIntroductionConfigs} labelCol={3} wrapperCol={9} />
                <div className={styles.addBtnContainer}>
                    <Form.Item className={styles.saveBtn}>
                        <BaseButton type='primary' htmlType='submit' className={styles.submitBtn}>
                            保存
                        </BaseButton>
                    </Form.Item>
                    <Form.Item>
                        <BaseButton
                            htmlType='button'
                            type='dashed'
                            className={styles.primaryBtn}
                            onClick={() => {
                                navigate('/purchase/list');
                            }}
                        >
                            取消
                        </BaseButton>
                    </Form.Item>
                </div>
            </Form>
            <Modal
                title='该农户尚未注册账号'
                open={checkRegisterModal}
                onOk={handleRegister}
                onCancel={() => setCheckRegisterModal(false)}
            >
                <p>请确认农户信息是否正确，确认后系统将自动为该农户注册账号: </p>
                <div style={{ marginTop: 20 }}>
                    <p>农户姓名：{formValues?.farmerName}</p>
                    <p>联系方式：{formValues?.phoneNumber}</p>
                </div>
            </Modal>

            <BaseModal
                {...successConfig}
                className='employees_header'
                // closeIcon={<CheckCircleFilled className={styles.employess_icon} rev={undefined} />}
            >
                <Form
                    name='creatEmployeesForm'
                    form={addAccountForm}
                    className='employess-label-title'
                    labelAlign='left'
                >
                    {<FilterForm showMode itemConfig={successConfigs} />}
                </Form>
            </BaseModal>
        </BaseCard>
    );
};

export default FoodAdd;
